package expect

import (
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"

	mDeploy "git.code.oa.com/RondaServing/ServingController/resource/model/deployment"
)

type expectType struct {
	all     allExpectType
	bySrv   mDeploy.ByServerType
	byModel mDeploy.ByModelType
}

var (
	mutex      sync.Mutex
	allExpects expectType
)

func initManager() {
	allExpects.all = make(allExpectType)
	allExpects.bySrv = make(mDeploy.ByServerType)
	allExpects.byModel = make(mDeploy.ByModelType)
}

type allExpectType map[mDeploy.ModelDeployKey]ModelExpect

func (a *allExpectType) replace(expect ModelExpect) {
	key := expect.GetKey()
	(*a)[key] = expect
}

func (a *allExpectType) delete(key mDeploy.ModelDeployKey) {
	if _, ok := (*a)[key]; !ok {
		return
	}
	delete(*a, key)
}

func (a *allExpectType) get(key mDeploy.ModelDeployKey) (ModelExpect, bool) {
	depl, ok := (*a)[key]
	return depl, ok
}

func (a *allExpectType) len() int {
	return len(*a)
}

func replaceModelExpectInCache(expect ModelExpect) {
	mutex.Lock()
	defer mutex.Unlock()
	allExpects.all.replace(expect)
	allExpects.bySrv.Replace(expect.GetKey())
	allExpects.byModel.Replace(expect.GetKey())
}

// ReplaceModelExpect 增加/更新ModelExpect
func ReplaceModelExpect(expect ModelExpect) error {
	if err := replaceModelExpectInCkpt(expect); err != nil {
		log.Errorf("replace model expect(%+v) failed:%s", expect.GetKey(), err.Error())
		return err
	}
	replaceModelExpectInCache(expect)
	return nil
}

func deleteModelExpectInCache(deplKey mDeploy.ModelDeployKey) {
	mutex.Lock()
	defer mutex.Unlock()
	allExpects.all.delete(deplKey)
	allExpects.bySrv.Delete(deplKey)
	allExpects.byModel.Delete(deplKey)
}

// DeleteModelExpect 删除指定ModelExpect
func DeleteModelExpect(deplKey mDeploy.ModelDeployKey) error {
	if err := deleteModelExpectInCkpt(deplKey); err != nil {
		log.Errorf("delete model expect(%+v) error: %s", deplKey, err.Error())
		return err
	}
	deleteModelExpectInCache(deplKey)
	return nil
}

func getModelExpectByServerInCache(serverID int) map[mDeploy.ModelLabel]ModelExpect {
	mutex.Lock()
	defer mutex.Unlock()
	lables := allExpects.bySrv.Get(serverID)
	if len(lables) == 0 {
		return nil
	}
	retDeployments := make(map[mDeploy.ModelLabel]ModelExpect)
	for lb := range lables {
		depl, ok := allExpects.all.get(mDeploy.ModelDeployKey{
			ServerID:     serverID,
			ModelName:    lb.ModelName,
			ModelVersion: lb.ModelVersion,
		})
		if !ok {
			log.Errorf("Can't find server:%+v label:%+v", serverID, lb)
			continue
		}
		retDeployments[lb] = depl
	}
	return retDeployments
}

// DeleteModelExpectsByServer 删除指定ServerID下的所有expect
func DeleteModelExpectsByServer(serverID int) error {
	expects := getModelExpectByServerInCache(serverID)
	for _, expc := range expects {
		deplKey := expc.GetKey()
		if err := deleteModelExpectInCkpt(deplKey); err != nil {
			log.Errorf("delete model expect(%+v) error: %s", deplKey, err.Error())
		}
		deleteModelExpectInCache(deplKey)
	}
	return nil
}

// GetModelExpect 获取ModelExpect
func GetModelExpect(deplKey mDeploy.ModelDeployKey) (ModelExpect, bool) {
	mutex.Lock()
	defer mutex.Unlock()
	return allExpects.all.get(deplKey)
}

// GetModelExpectsByServer 获取指定服务下所有的 ModelExpect
func GetModelExpectsByServer(serverID int) map[mDeploy.ModelLabel]ModelExpect {
	return getModelExpectByServerInCache(serverID)
}

// GetAllServerID 获取所有ServerID
func GetAllServerID() []int {
	mutex.Lock()
	defer mutex.Unlock()
	return allExpects.bySrv.GetAllServerID()
}

// GetAllExpectLen 获取所有expect的数量
func GetAllExpectLen() int {
	mutex.Lock()
	defer mutex.Unlock()
	return allExpects.all.len()
}

// GetVersionsUsedByModel 获取指定 ModelName 下有部署到服务的 ModelVersion
func GetVersionsUsedByModel(modelName string) []string {
	mutex.Lock()
	defer mutex.Unlock()
	return allExpects.byModel.GetVersionsUse(modelName)
}
